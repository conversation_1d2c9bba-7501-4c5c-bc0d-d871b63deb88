"""Add content validation constraint to tg_content table

Revision ID: 20250918_120000
Revises: 20250911_170000_format_messages_table_content
Create Date: 2025-09-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20250918_120000'
down_revision = '20250911_170000_format_messages_table_content'
branch_labels = None
depends_on = None

def upgrade():
    """Add check constraint to prevent empty content in tg_content table."""
    # First, clean up any existing empty content records
    op.execute("DELETE FROM tg_content WHERE content IS NULL OR TRIM(content) = ''")
    
    # Add the check constraint
    op.create_check_constraint(
        'check_content_not_empty',
        'tg_content',
        'LENGTH(TRIM(content)) > 0'
    )

def downgrade():
    """Remove the content validation constraint."""
    op.drop_constraint('check_content_not_empty', 'tg_content', type_='check')
