#!/usr/bin/env python3
"""
Test script to verify the enhanced user_id extraction system.
"""

from unittest.mock import Mock
from utils.logger import Logger

test_logger = Logger("test_enhanced_user_id")

def test_enhanced_user_id_extraction():
    """Test the enhanced user_id extraction logic."""
    test_logger.info("=== TESTING ENHANCED USER_ID EXTRACTION ===")
    
    # Mock the enhanced extraction method
    def mock_extract_enhanced_user_id(message, entity=None):
        """Mock implementation of the enhanced user_id extraction."""
        try:
            # Base user_id extraction from message.from_id
            base_user_id = None
            peer_type = None
            
            if hasattr(message, 'from_id') and message.from_id:
                if hasattr(message.from_id, 'user_id'):
                    base_user_id = str(message.from_id.user_id)
                    peer_type = "user"
                elif hasattr(message.from_id, 'channel_id'):
                    base_user_id = str(message.from_id.channel_id)
                    peer_type = "channel"
                elif hasattr(message.from_id, 'chat_id'):
                    base_user_id = str(message.from_id.chat_id)
                    peer_type = "chat"
            
            if base_user_id is None:
                return "0"
            
            # If entity context is available, create enhanced user_id
            if entity is not None:
                entity_type = getattr(entity, 'type', 'unknown')
                is_private = getattr(entity, 'is_private', 0)
                privacy = "private" if is_private == 1 else "public"
                return f"{base_user_id}:{peer_type}:{entity_type}:{privacy}"
            else:
                return f"{base_user_id}:{peer_type}"
                
        except Exception:
            return "0"
    
    # Test Scenario 1: User message in private group
    test_logger.info("Scenario 1: User message in private group")
    user_message = Mock()
    user_message.from_id = Mock()
    user_message.from_id.user_id = 123456789
    
    private_group_entity = Mock()
    private_group_entity.type = "group"
    private_group_entity.is_private = 1
    
    result = mock_extract_enhanced_user_id(user_message, private_group_entity)
    expected = "123456789:user:group:private"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test Scenario 2: Channel message in public channel
    test_logger.info("Scenario 2: Channel message in public channel")
    channel_message = Mock()
    channel_message.from_id = Mock()
    channel_message.from_id.channel_id = 987654321
    # Remove user_id attribute to simulate PeerChannel
    del channel_message.from_id.user_id
    
    public_channel_entity = Mock()
    public_channel_entity.type = "channel"
    public_channel_entity.is_private = 0
    
    result = mock_extract_enhanced_user_id(channel_message, public_channel_entity)
    expected = "987654321:channel:channel:public"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test Scenario 3: Group chat message in private supergroup
    test_logger.info("Scenario 3: Group chat message in private supergroup")
    chat_message = Mock()
    chat_message.from_id = Mock()
    chat_message.from_id.chat_id = 555666777
    # Remove user_id and channel_id attributes to simulate PeerChat
    del chat_message.from_id.user_id
    del chat_message.from_id.channel_id
    
    private_supergroup_entity = Mock()
    private_supergroup_entity.type = "group"
    private_supergroup_entity.is_private = 1
    
    result = mock_extract_enhanced_user_id(chat_message, private_supergroup_entity)
    expected = "555666777:chat:group:private"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test Scenario 4: Message without entity context
    test_logger.info("Scenario 4: Message without entity context")
    user_message_no_context = Mock()
    user_message_no_context.from_id = Mock()
    user_message_no_context.from_id.user_id = 111222333
    
    result = mock_extract_enhanced_user_id(user_message_no_context, None)
    expected = "111222333:user"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test Scenario 5: System/anonymous message
    test_logger.info("Scenario 5: System/anonymous message")
    system_message = Mock()
    system_message.from_id = None
    
    result = mock_extract_enhanced_user_id(system_message, private_group_entity)
    expected = "0"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test Scenario 6: User in public channel
    test_logger.info("Scenario 6: User in public channel")
    user_in_channel = Mock()
    user_in_channel.from_id = Mock()
    user_in_channel.from_id.user_id = 444555666
    
    public_channel = Mock()
    public_channel.type = "channel"
    public_channel.is_private = 0
    
    result = mock_extract_enhanced_user_id(user_in_channel, public_channel)
    expected = "444555666:user:channel:public"
    test_logger.info(f"  Result: '{result}' (expected: '{expected}')")
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    test_logger.info("✅ All enhanced user_id extraction tests passed!")

def explain_enhanced_format():
    """Explain the enhanced user_id format."""
    test_logger.info("=== ENHANCED USER_ID FORMAT EXPLANATION ===")
    
    test_logger.info("Enhanced user_id format: {base_id}:{peer_type}:{entity_type}:{privacy}")
    test_logger.info("")
    test_logger.info("Components:")
    test_logger.info("  - base_id: The actual Telegram ID (user_id, channel_id, or chat_id)")
    test_logger.info("  - peer_type: Type of sender (user, channel, chat)")
    test_logger.info("  - entity_type: Type of entity where message was sent (channel, group)")
    test_logger.info("  - privacy: Whether the entity is private or public")
    test_logger.info("")
    test_logger.info("Examples:")
    test_logger.info("  - '123456789:user:group:private' = User 123456789 in a private group")
    test_logger.info("  - '987654321:channel:channel:public' = Channel 987654321 in a public channel")
    test_logger.info("  - '555666777:chat:group:private' = Chat 555666777 in a private supergroup")
    test_logger.info("  - '111222333:user' = User 111222333 (no entity context)")
    test_logger.info("  - '0' = System/anonymous message")
    test_logger.info("")
    test_logger.info("Benefits:")
    test_logger.info("  ✅ Handles all Telethon peer types (PeerUser, PeerChannel, PeerChat)")
    test_logger.info("  ✅ Distinguishes between private and public entities")
    test_logger.info("  ✅ Provides context about message source and destination")
    test_logger.info("  ✅ Maintains backward compatibility with fallback to basic ID")

def main():
    """Main test function."""
    test_logger.info("Starting enhanced user_id extraction tests...")
    
    try:
        # Test the enhanced extraction logic
        test_enhanced_user_id_extraction()
        
        # Explain the format
        explain_enhanced_format()
        
        test_logger.info("🎉 ALL TESTS PASSED! Enhanced user_id extraction is working correctly.")
        
    except Exception as e:
        test_logger.error(f"❌ TEST FAILED: {str(e)}")
        raise

if __name__ == "__main__":
    main()
