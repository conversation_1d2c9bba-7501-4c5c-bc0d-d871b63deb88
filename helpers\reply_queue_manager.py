#!/usr/bin/env python3
"""
Reply Queue Manager for handling reply messages when parent messages aren't available yet.
This handles the case where new messages arrive before historical data is fully processed.
"""

import asyncio
import time
from typing import Dict, List, Optional, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from utils.logger import Logger
from models.tg_content import TgContent

reply_queue_logger = Logger("reply_queue_manager")

class ReplyQueueManager:
    """Manages a queue of reply messages that need to be reprocessed when their parents become available."""
    
    def __init__(self):
        self.pending_replies: Dict[int, List[Dict]] = {}  # entity_id -> list of pending reply messages
        self.parent_waitlist: Dict[str, Set[int]] = {}  # "entity_id:message_id" -> set of reply message IDs waiting for this parent
        self.max_wait_time = 3600  # Maximum time to wait for a parent message (1 hour)
        self.cleanup_interval = 300  # Clean up expired entries every 5 minutes
        self.last_cleanup = time.time()
    
    def add_pending_reply(self, reply_message: TgContent, parent_message_id: int) -> None:
        """
        Add a reply message to the pending queue.
        
        Args:
            reply_message: The reply message that couldn't find its parent
            parent_message_id: The message ID of the expected parent
        """
        entity_id = reply_message.entities_id
        parent_key = f"{entity_id}:{parent_message_id}"
        
        # Add to pending replies
        if entity_id not in self.pending_replies:
            self.pending_replies[entity_id] = []
        
        reply_info = {
            'message_id': reply_message.message_id,
            'content_id': reply_message.id,
            'parent_message_id': parent_message_id,
            'added_time': time.time(),
            'retry_count': 0
        }
        
        self.pending_replies[entity_id].append(reply_info)
        
        # Add to parent waitlist
        if parent_key not in self.parent_waitlist:
            self.parent_waitlist[parent_key] = set()
        
        self.parent_waitlist[parent_key].add(reply_message.message_id)
        
        reply_queue_logger.info(
            "Added reply message to pending queue",
            reply_message_id=reply_message.message_id,
            parent_message_id=parent_message_id,
            entity_id=entity_id,
            pending_count=len(self.pending_replies[entity_id])
        )
    
    def check_parent_available(self, entity_id: int, message_id: int) -> List[int]:
        """
        Check if any pending replies are waiting for this parent message.
        
        Args:
            entity_id: The entity ID
            message_id: The message ID that just became available
            
        Returns:
            List of reply message IDs that were waiting for this parent
        """
        parent_key = f"{entity_id}:{message_id}"
        
        if parent_key in self.parent_waitlist:
            waiting_replies = list(self.parent_waitlist[parent_key])
            # Remove from waitlist since parent is now available
            del self.parent_waitlist[parent_key]
            
            reply_queue_logger.info(
                "Parent message became available",
                parent_message_id=message_id,
                entity_id=entity_id,
                waiting_replies_count=len(waiting_replies)
            )
            
            return waiting_replies
        
        return []
    
    def get_pending_replies_for_entity(self, entity_id: int) -> List[Dict]:
        """
        Get all pending replies for a specific entity.
        
        Args:
            entity_id: The entity ID
            
        Returns:
            List of pending reply information
        """
        return self.pending_replies.get(entity_id, [])
    
    def remove_processed_reply(self, entity_id: int, message_id: int) -> bool:
        """
        Remove a reply from the pending queue after it has been processed.
        
        Args:
            entity_id: The entity ID
            message_id: The reply message ID that was processed
            
        Returns:
            True if the reply was found and removed, False otherwise
        """
        if entity_id in self.pending_replies:
            for i, reply_info in enumerate(self.pending_replies[entity_id]):
                if reply_info['message_id'] == message_id:
                    removed_reply = self.pending_replies[entity_id].pop(i)
                    
                    # Also remove from parent waitlist
                    parent_key = f"{entity_id}:{removed_reply['parent_message_id']}"
                    if parent_key in self.parent_waitlist:
                        self.parent_waitlist[parent_key].discard(message_id)
                        if not self.parent_waitlist[parent_key]:
                            del self.parent_waitlist[parent_key]
                    
                    reply_queue_logger.info(
                        "Removed processed reply from queue",
                        reply_message_id=message_id,
                        entity_id=entity_id
                    )
                    return True
        
        return False
    
    def cleanup_expired_entries(self) -> None:
        """Remove expired entries from the queue."""
        current_time = time.time()
        
        # Only run cleanup periodically
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        self.last_cleanup = current_time
        expired_count = 0
        
        # Clean up expired pending replies
        for entity_id in list(self.pending_replies.keys()):
            replies = self.pending_replies[entity_id]
            non_expired = []
            
            for reply_info in replies:
                if current_time - reply_info['added_time'] < self.max_wait_time:
                    non_expired.append(reply_info)
                else:
                    expired_count += 1
                    # Remove from parent waitlist too
                    parent_key = f"{entity_id}:{reply_info['parent_message_id']}"
                    if parent_key in self.parent_waitlist:
                        self.parent_waitlist[parent_key].discard(reply_info['message_id'])
                        if not self.parent_waitlist[parent_key]:
                            del self.parent_waitlist[parent_key]
            
            if non_expired:
                self.pending_replies[entity_id] = non_expired
            else:
                del self.pending_replies[entity_id]
        
        if expired_count > 0:
            reply_queue_logger.info(
                "Cleaned up expired reply queue entries",
                expired_count=expired_count
            )
    
    async def retry_pending_replies(self, session: AsyncSession, entity_id: int) -> List[TgContent]:
        """
        Check if any pending replies for an entity can now be processed.
        
        Args:
            session: Database session
            entity_id: The entity ID to check
            
        Returns:
            List of reply messages that can now be processed
        """
        self.cleanup_expired_entries()
        
        if entity_id not in self.pending_replies:
            return []
        
        processable_replies = []
        remaining_replies = []
        
        for reply_info in self.pending_replies[entity_id]:
            # Check if parent message now exists
            stmt = select(TgContent).where(
                TgContent.entities_id == entity_id,
                TgContent.message_id == reply_info['parent_message_id']
            )
            result = await session.execute(stmt)
            parent_message = result.scalars().first()
            
            if parent_message:
                # Parent is now available, get the reply message
                stmt = select(TgContent).where(TgContent.id == reply_info['content_id'])
                result = await session.execute(stmt)
                reply_message = result.scalars().first()
                
                if reply_message:
                    processable_replies.append(reply_message)
                    reply_queue_logger.info(
                        "Reply message can now be processed",
                        reply_message_id=reply_message.message_id,
                        parent_message_id=reply_info['parent_message_id'],
                        entity_id=entity_id
                    )
                else:
                    # Reply message no longer exists, remove from queue
                    pass
            else:
                # Parent still not available, keep in queue
                reply_info['retry_count'] += 1
                remaining_replies.append(reply_info)
        
        # Update the pending replies list
        if remaining_replies:
            self.pending_replies[entity_id] = remaining_replies
        else:
            del self.pending_replies[entity_id]
        
        return processable_replies
    
    def get_queue_stats(self) -> Dict:
        """Get statistics about the current queue state."""
        total_pending = sum(len(replies) for replies in self.pending_replies.values())
        entities_with_pending = len(self.pending_replies)
        
        return {
            'total_pending_replies': total_pending,
            'entities_with_pending': entities_with_pending,
            'parent_waitlist_size': len(self.parent_waitlist)
        }

# Global instance
reply_queue_manager = ReplyQueueManager()
