#!/usr/bin/env python3
"""
Script to check message_date values in tg_content table and identify any incorrect values.
"""

import asyncio
import sys
from services.database import DatabaseService
from utils.logger import Logger
from utils.config_loader import ConfigLoader
from models.tg_content import TgContent
from sqlalchemy import select, func
import time
from datetime import datetime

check_logger = Logger("message_date_check")

async def check_message_dates():
    """Check message_date values in tg_content table."""
    try:
        check_logger.info("Checking message_date values in tg_content table")
        
        # Load configuration
        config = ConfigLoader.load_config()
        
        # Initialize database service
        db_service = DatabaseService(config)
        await db_service.initialize()
        session = await db_service.get_session()
        
        try:
            # Get all records to check message_date values
            stmt = select(TgContent).limit(10)  # Check first 10 records as sample
            result = await session.execute(stmt)
            records = result.scalars().all()
            
            current_timestamp = int(time.time())
            check_logger.info(f"Current timestamp: {current_timestamp} ({datetime.fromtimestamp(current_timestamp)})")
            
            issues_found = 0
            for record in records:
                # Check if message_date looks like a valid epoch timestamp
                # Valid epoch timestamps should be between 1970 and current time + some buffer
                min_valid_timestamp = 0  # 1970-01-01
                max_valid_timestamp = current_timestamp + (365 * 24 * 3600)  # Current time + 1 year
                
                if record.message_date < min_valid_timestamp or record.message_date > max_valid_timestamp:
                    issues_found += 1
                    check_logger.warning(
                        f"Invalid message_date found",
                        record_id=record.id,
                        message_date=record.message_date,
                        created_at=record.created_at,
                        created_at_readable=datetime.fromtimestamp(record.created_at) if record.created_at > 0 else "Invalid"
                    )
                else:
                    check_logger.info(
                        f"Valid message_date",
                        record_id=record.id,
                        message_date=record.message_date,
                        message_date_readable=datetime.fromtimestamp(record.message_date),
                        created_at=record.created_at,
                        created_at_readable=datetime.fromtimestamp(record.created_at)
                    )
            
            # Get count of all records
            count_stmt = select(func.count(TgContent.id))
            count_result = await session.execute(count_stmt)
            total_records = count_result.scalar()
            
            # Get statistics about message_date values
            stats_stmt = select(
                func.min(TgContent.message_date).label('min_date'),
                func.max(TgContent.message_date).label('max_date'),
                func.avg(TgContent.message_date).label('avg_date')
            )
            stats_result = await session.execute(stats_stmt)
            stats = stats_result.first()
            
            check_logger.info(
                "Message date statistics",
                total_records=total_records,
                min_date=stats.min_date,
                max_date=stats.max_date,
                avg_date=int(stats.avg_date) if stats.avg_date else None,
                min_date_readable=datetime.fromtimestamp(stats.min_date) if stats.min_date and stats.min_date > 0 else "Invalid",
                max_date_readable=datetime.fromtimestamp(stats.max_date) if stats.max_date and stats.max_date < current_timestamp + (365 * 24 * 3600) else "Invalid"
            )
            
            if issues_found > 0:
                check_logger.warning(f"Found {issues_found} records with potentially invalid message_date values in sample")
                return False
            else:
                check_logger.info("All sampled records have valid message_date values")
                return True
                
        finally:
            await session.close()
            
    except Exception as e:
        check_logger.error("Error during message_date check", error=str(e))
        return False

async def main():
    """Main check function."""
    success = await check_message_dates()
    if success:
        check_logger.info("Message date check completed successfully")
        sys.exit(0)
    else:
        check_logger.error("Message date check found issues")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
