from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Index, String, Text, func, Integer, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship

class Base(DeclarativeBase):
    pass

class Register(Base):
    __tablename__ = 'register'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, nullable=False, unique=True)  # Project ID column positioned after id
    phone_number: Mapped[str] = mapped_column(String(255), nullable=False)
    api_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    api_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    tg_bot_token: Mapped[str] = mapped_column(String(255), nullable=False)
    tg_chat_id: Mapped[str] = mapped_column(String(255), nullable=True)
    message_thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Message thread ID for subtopics
    scheduler_to_post: Mapped[int] = mapped_column(Integer, nullable=False)
    active_post_hour_start: Mapped[int] = mapped_column(Integer, nullable=False)
    active_post_hour_end: Mapped[int] = mapped_column(Integer, nullable=False)
    active_reply_hour_start: Mapped[int] = mapped_column(Integer, nullable=False)
    active_reply_hour_end: Mapped[int] = mapped_column(Integer, nullable=False)
    keyword: Mapped[str] = mapped_column(String(255), nullable=True)
    hashtag: Mapped[str] = mapped_column(String(255), nullable=True)
    model_selection: Mapped[int] = mapped_column(Integer, nullable=False)
    llm_api_key: Mapped[str] = mapped_column(Text, nullable=False)
    llm_max_token: Mapped[int] = mapped_column(Integer, nullable=False)
    llm_temperature: Mapped[float] = mapped_column(Float, nullable=False)
    llm_top_p: Mapped[float] = mapped_column(Float, nullable=False)
    llm_system_prompt: Mapped[str] = mapped_column(Text, nullable=False)
    llm_user_prompt: Mapped[str] = mapped_column(Text, nullable=False)
    reply_mentions: Mapped[int] = mapped_column(Integer, nullable=False)  # TINYINT
    expiry_date: Mapped[int] = mapped_column(BigInteger, nullable=False)
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # TINYINT with default
    is_verify: Mapped[int] = mapped_column(Integer, nullable=False)  # TINYINT
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

class TgEntity(Base):
    __tablename__ = 'tg_entities'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(255), nullable=False)  # public/private
    tg_id: Mapped[str] = mapped_column(String(255), nullable=False)  # tg internal id for the channel/group
    access_hash: Mapped[str] = mapped_column(String(255), nullable=True)
    is_private: Mapped[int] = mapped_column(Integer, nullable=True)  # TINYINT

    # Relationships
    tg_contents: Mapped[list["TgContent"]] = relationship("TgContent", back_populates="entity")
    messages: Mapped[list["Message"]] = relationship("Message", back_populates="entity")

class TgContent(Base):
    __tablename__ = 'tg_content'

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    entities_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=False)
    thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)
    user_id: Mapped[str] = mapped_column(String(255), nullable=False)
    message_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    message_date: Mapped[int] = mapped_column(BigInteger, nullable=False)  # Epoch Time
    reply_to: Mapped[int] = mapped_column(BigInteger, nullable=True)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

    # Relationships
    entity: Mapped["TgEntity"] = relationship("TgEntity", back_populates="tg_contents")

class Message(Base):
    __tablename__ = 'messages'

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, nullable=False)
    message_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    entities_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    message_used: Mapped[str] = mapped_column(Text, nullable=True)
    reply_to: Mapped[int] = mapped_column(BigInteger, nullable=True)
    reply_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

    # Relationships
    entity: Mapped["TgEntity"] = relationship("TgEntity", back_populates="messages")

class OtpRequest(Base):
    __tablename__ = 'otp_requests'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    phone_number: Mapped[str] = mapped_column(String(255), nullable=False)
    otp_code_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    api_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    api_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)
    expires_at: Mapped[int] = mapped_column(BigInteger, nullable=False)
    is_verified: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # TINYINT


class RegisterTgEntity(Base):
    __tablename__ = 'register_tg_entities'
    __table_args__ = (
        Index('idx_register_tg_entities_project_id', 'project_id'),
        Index('idx_register_tg_entities_tg_entity_id', 'tg_entity_id'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, ForeignKey('register.project_id'), nullable=False)
    tg_entity_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=False)
    message_thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Message thread ID for subtopics

    # Relationships
    register: Mapped["Register"] = relationship("Register", backref="register_tg_entities")
    tg_entity: Mapped["TgEntity"] = relationship("TgEntity", backref="register_tg_entities")


class BotState(Base):
    __tablename__ = 'bot_state'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, ForeignKey('register.project_id'), nullable=False, unique=True)
    last_post_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    last_reply_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    
    # Relationship to Register
    register: Mapped["Register"] = relationship("Register", backref="bot_state")