from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Integer, String, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base

class TgContent(Base):
    __tablename__ = 'tg_content'

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    entities_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=False)
    thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Made nullable
    user_id: Mapped[str] = mapped_column(String(255), nullable=False)
    message_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    message_date: Mapped[int] = mapped_column(BigInteger, nullable=False)  # Epoch Time
    reply_to: Mapped[int] = mapped_column(BigInteger, nullable=True)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

    # Relationships
    entity: Mapped["TgEntity"] = relationship("TgEntity", back_populates="tg_contents")