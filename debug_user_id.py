#!/usr/bin/env python3
"""
Debug script to investigate user_id extraction issues.
"""

import asyncio
from unittest.mock import Mock
from utils.logger import Logger
from utils.config_loader import ConfigLoader
from services.database import DatabaseService

debug_logger = Logger("debug_user_id")

async def check_recent_messages():
    """Check recent messages in the database to see user_id distribution."""
    debug_logger.info("=== CHECKING RECENT MESSAGES ===")
    
    try:
        # Load config
        config_loader = ConfigLoader()
        config = config_loader.load_config()
        
        # Initialize database service
        db_service = DatabaseService(config['database'])
        await db_service.initialize()
        
        session = await db_service.get_session()
        try:
            from sqlalchemy import select, func, text
            from models.tg_content import TgContent
            
            # Check user_id distribution
            debug_logger.info("Checking user_id distribution in recent messages...")
            
            stmt = select(TgContent.user_id, func.count(TgContent.id).label('count')).group_by(TgContent.user_id).order_by(func.count(TgContent.id).desc()).limit(10)
            result = await session.execute(stmt)
            user_id_counts = result.all()
            
            debug_logger.info("Top user_ids by message count:")
            for user_id, count in user_id_counts:
                debug_logger.info(f"  user_id '{user_id}': {count} messages")
            
            # Check recent messages with details
            debug_logger.info("Recent messages with user_id details:")
            
            stmt = select(TgContent).order_by(TgContent.created_at.desc()).limit(5)
            result = await session.execute(stmt)
            recent_messages = result.scalars().all()
            
            for msg in recent_messages:
                debug_logger.info(f"  Message {msg.message_id}: user_id='{msg.user_id}', entity={msg.entities_id}")
                debug_logger.info(f"    Content preview: {msg.content[:50]}...")
                debug_logger.info(f"    Reply to: {msg.reply_to}")
            
            # Check if there are any non-zero user_ids
            stmt = select(func.count(TgContent.id)).where(TgContent.user_id != "0")
            result = await session.execute(stmt)
            non_zero_count = result.scalar()
            
            debug_logger.info(f"Messages with non-zero user_id: {non_zero_count}")
            
            if non_zero_count > 0:
                debug_logger.info("Sample messages with non-zero user_id:")
                stmt = select(TgContent).where(TgContent.user_id != "0").limit(3)
                result = await session.execute(stmt)
                non_zero_messages = result.scalars().all()
                
                for msg in non_zero_messages:
                    debug_logger.info(f"  Message {msg.message_id}: user_id='{msg.user_id}'")
            
        finally:
            await session.close()
            
    except Exception as e:
        debug_logger.error(f"Database error: {str(e)}")

def test_message_scenarios():
    """Test different message scenarios to understand the issue."""
    debug_logger.info("=== TESTING MESSAGE SCENARIOS ===")

    # Scenario 1: Regular user message
    debug_logger.info("Scenario 1: Regular user message")
    user_message = Mock()
    user_message.from_id = Mock()
    user_message.from_id.user_id = 123456789

    user_id = None
    if hasattr(user_message, 'from_id') and user_message.from_id:
        if hasattr(user_message.from_id, 'user_id'):
            user_id = str(user_message.from_id.user_id)
        elif hasattr(user_message.from_id, 'channel_id'):
            user_id = str(user_message.from_id.channel_id)
        elif hasattr(user_message.from_id, 'chat_id'):
            user_id = str(user_message.from_id.chat_id)

    if user_id is None:
        user_id = "0"

    debug_logger.info(f"  Result: user_id = '{user_id}' (expected: '123456789')")

    # Scenario 2: Channel message
    debug_logger.info("Scenario 2: Channel message")
    channel_message = Mock()
    channel_message.from_id = Mock()
    channel_message.from_id.channel_id = 987654321
    # Simulate no user_id attribute
    if hasattr(channel_message.from_id, 'user_id'):
        delattr(channel_message.from_id, 'user_id')

    user_id = None
    if hasattr(channel_message, 'from_id') and channel_message.from_id:
        if hasattr(channel_message.from_id, 'user_id'):
            user_id = str(channel_message.from_id.user_id)
        elif hasattr(channel_message.from_id, 'channel_id'):
            user_id = str(channel_message.from_id.channel_id)
        elif hasattr(channel_message.from_id, 'chat_id'):
            user_id = str(channel_message.from_id.chat_id)

    if user_id is None:
        user_id = "0"

    debug_logger.info(f"  Result: user_id = '{user_id}' (expected: '987654321')")

    # Scenario 3: Group message (NEW - this was the missing case!)
    debug_logger.info("Scenario 3: Group message (PeerChat)")
    group_message = Mock()
    group_message.from_id = Mock()
    group_message.from_id.chat_id = 555666777
    # Simulate no user_id or channel_id attributes
    if hasattr(group_message.from_id, 'user_id'):
        delattr(group_message.from_id, 'user_id')
    if hasattr(group_message.from_id, 'channel_id'):
        delattr(group_message.from_id, 'channel_id')

    user_id = None
    if hasattr(group_message, 'from_id') and group_message.from_id:
        if hasattr(group_message.from_id, 'user_id'):
            user_id = str(group_message.from_id.user_id)
        elif hasattr(group_message.from_id, 'channel_id'):
            user_id = str(group_message.from_id.channel_id)
        elif hasattr(group_message.from_id, 'chat_id'):
            user_id = str(group_message.from_id.chat_id)

    if user_id is None:
        user_id = "0"

    debug_logger.info(f"  Result: user_id = '{user_id}' (expected: '555666777')")

    # Scenario 4: Message with no from_id (system/anonymous)
    debug_logger.info("Scenario 4: System/anonymous message")
    system_message = Mock()
    system_message.from_id = None

    user_id = None
    if hasattr(system_message, 'from_id') and system_message.from_id:
        if hasattr(system_message.from_id, 'user_id'):
            user_id = str(system_message.from_id.user_id)
        elif hasattr(system_message.from_id, 'channel_id'):
            user_id = str(system_message.from_id.channel_id)
        elif hasattr(system_message.from_id, 'chat_id'):
            user_id = str(system_message.from_id.chat_id)

    if user_id is None:
        user_id = "0"

    debug_logger.info(f"  Result: user_id = '{user_id}' (expected: '0')")

    # Scenario 5: Message with empty from_id object
    debug_logger.info("Scenario 5: Message with empty from_id object")
    empty_from_message = Mock()
    empty_from_message.from_id = Mock()
    # Remove all ID attributes
    if hasattr(empty_from_message.from_id, 'user_id'):
        delattr(empty_from_message.from_id, 'user_id')
    if hasattr(empty_from_message.from_id, 'channel_id'):
        delattr(empty_from_message.from_id, 'channel_id')
    if hasattr(empty_from_message.from_id, 'chat_id'):
        delattr(empty_from_message.from_id, 'chat_id')

    user_id = None
    if hasattr(empty_from_message, 'from_id') and empty_from_message.from_id:
        if hasattr(empty_from_message.from_id, 'user_id'):
            user_id = str(empty_from_message.from_id.user_id)
        elif hasattr(empty_from_message.from_id, 'channel_id'):
            user_id = str(empty_from_message.from_id.channel_id)
        elif hasattr(empty_from_message.from_id, 'chat_id'):
            user_id = str(empty_from_message.from_id.chat_id)

    if user_id is None:
        user_id = "0"

    debug_logger.info(f"  Result: user_id = '{user_id}' (expected: '0')")

async def check_telethon_message_structure():
    """Check what a real Telethon message structure looks like."""
    debug_logger.info("=== TELETHON MESSAGE STRUCTURE INFO ===")
    
    debug_logger.info("In Telethon, message.from_id can be:")
    debug_logger.info("  - PeerUser(user_id=123456) for user messages")
    debug_logger.info("  - PeerChannel(channel_id=789012) for channel messages")
    debug_logger.info("  - PeerChat(chat_id=345678) for group messages")
    debug_logger.info("  - None for anonymous/system messages")
    
    debug_logger.info("Our extraction logic should handle:")
    debug_logger.info("  - message.from_id.user_id (for PeerUser) ✅")
    debug_logger.info("  - message.from_id.channel_id (for PeerChannel) ✅")
    debug_logger.info("  - message.from_id.chat_id (for PeerChat) ✅ FIXED!")

    debug_logger.info("✅ All Telethon peer types are now handled!")

async def main():
    """Main debug function."""
    debug_logger.info("Starting user_id extraction debugging...")
    
    try:
        # Test message scenarios
        test_message_scenarios()
        
        # Check Telethon structure info
        await check_telethon_message_structure()
        
        # Check recent messages in database
        await check_recent_messages()
        
        debug_logger.info("🔍 Debug analysis complete!")
        
    except Exception as e:
        debug_logger.error(f"❌ DEBUG FAILED: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
