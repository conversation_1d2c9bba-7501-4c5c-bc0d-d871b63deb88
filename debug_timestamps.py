#!/usr/bin/env python3
"""
Debug script to check timestamp calculations and timezone issues.
"""

import time
from datetime import datetime
import pytz

print("=== Timestamp Debug Information ===")

# Current time in different formats
current_time = time.time()
print(f"Current time.time(): {current_time}")
print(f"Current datetime.now(): {datetime.now()}")
print(f"Current datetime.utcnow(): {datetime.utcnow()}")

# Convert current timestamp to readable format
print(f"Current timestamp as datetime: {datetime.fromtimestamp(current_time)}")
print(f"Current timestamp as UTC datetime: {datetime.utcfromtimestamp(current_time)}")

# Check the sample timestamps from the database
sample_timestamps = [1757560780, 1757538274, 1757538193]
print("\n=== Sample Database Timestamps ===")
for ts in sample_timestamps:
    print(f"Timestamp {ts}:")
    print(f"  Local time: {datetime.fromtimestamp(ts)}")
    print(f"  UTC time: {datetime.utcfromtimestamp(ts)}")
    print(f"  Difference from now: {(ts - current_time) / 3600:.1f} hours")
    print()

# Check if there's a year offset issue
print("=== Year Check ===")
# Create a timestamp for today's date
today = datetime.now()
today_timestamp = int(today.timestamp())
print(f"Today's timestamp: {today_timestamp}")
print(f"Today as datetime: {datetime.fromtimestamp(today_timestamp)}")

# Check if the database timestamps are actually from 2024 instead of 2025
for ts in sample_timestamps:
    # Try subtracting a year (365.25 * 24 * 3600 seconds)
    year_ago_ts = ts - int(365.25 * 24 * 3600)
    print(f"Timestamp {ts} minus 1 year ({year_ago_ts}): {datetime.fromtimestamp(year_ago_ts)}")
