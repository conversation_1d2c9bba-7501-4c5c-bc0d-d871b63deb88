#!/usr/bin/env python3
"""
Test script to verify that message_date is now properly stored as epoch timestamp.
"""

import asyncio
import sys
from services.database import DatabaseService
from utils.logger import Logger
from utils.config_loader import ConfigLoader
from models.tg_content import TgContent
from datetime import datetime
import time

test_logger = Logger("message_date_test")

async def test_message_date_storage():
    """Test that message_date is stored as epoch timestamp."""
    try:
        test_logger.info("Testing message_date storage as epoch timestamp")
        
        # Load configuration
        config = ConfigLoader.load_config()
        
        # Initialize database service
        db_service = DatabaseService(config)
        await db_service.initialize()
        session = await db_service.get_session()
        
        try:
            # Create a test datetime object (simulating what comes from Telegram)
            test_datetime = datetime.now()
            test_timestamp = int(test_datetime.timestamp())
            
            test_logger.info(
                "Test data prepared",
                test_datetime=str(test_datetime),
                test_timestamp=test_timestamp
            )
            
            # Create a TgContent record with proper timestamp conversion
            # This simulates the fixed code path
            test_content = TgContent(
                entities_id=1,  # Assuming entity ID 1 exists
                message_id=999998,  # Use a high number to avoid conflicts
                user_id="test_user_timestamp",
                content="Test message for timestamp verification",
                message_date=test_timestamp,  # This should be an epoch timestamp
                created_at=test_timestamp
            )
            
            session.add(test_content)
            await session.commit()
            
            # Retrieve the record and verify the timestamp
            from sqlalchemy import select
            stmt = select(TgContent).where(TgContent.message_id == 999998)
            result = await session.execute(stmt)
            retrieved_record = result.scalars().first()
            
            if retrieved_record:
                # Verify that message_date is stored as an integer (epoch timestamp)
                if isinstance(retrieved_record.message_date, int):
                    # Convert back to datetime to verify it's correct
                    retrieved_datetime = datetime.fromtimestamp(retrieved_record.message_date)
                    
                    test_logger.info(
                        "SUCCESS: message_date stored correctly as epoch timestamp",
                        stored_timestamp=retrieved_record.message_date,
                        retrieved_datetime=str(retrieved_datetime),
                        original_datetime=str(test_datetime)
                    )
                    
                    # Clean up test record
                    await session.delete(retrieved_record)
                    await session.commit()
                    
                    return True
                else:
                    test_logger.error(
                        "FAILED: message_date not stored as integer",
                        stored_type=type(retrieved_record.message_date).__name__,
                        stored_value=retrieved_record.message_date
                    )
                    return False
            else:
                test_logger.error("FAILED: Could not retrieve test record")
                return False
                
        except Exception as e:
            test_logger.error("Error during message_date test", error=str(e))
            await session.rollback()
            return False
            
        finally:
            await session.close()
            
    except Exception as e:
        test_logger.error("Error during message_date test setup", error=str(e))
        return False

async def main():
    """Main test function."""
    success = await test_message_date_storage()
    if success:
        test_logger.info("Message date storage test PASSED")
        sys.exit(0)
    else:
        test_logger.error("Message date storage test FAILED")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
