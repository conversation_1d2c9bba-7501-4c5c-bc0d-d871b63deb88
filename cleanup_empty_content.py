#!/usr/bin/env python3
"""
Script to clean up empty content from tg_content table.
Run this script to remove any existing empty content records before applying the database constraint.
"""

import asyncio
import sys
from services.database import DatabaseService
from helpers.content_cleanup import cleanup_empty_tg_content, validate_tg_content_integrity
from utils.logger import Logger

cleanup_script_logger = Logger("cleanup_script")

async def main():
    """Main cleanup function."""
    try:
        cleanup_script_logger.info("Starting content cleanup process")
        
        # Initialize database service
        db_service = DatabaseService()
        session = await db_service.get_session()
        
        try:
            # First, validate current state
            cleanup_script_logger.info("Validating current content integrity")
            validation_before = await validate_tg_content_integrity(session)
            
            if validation_before['empty_records'] == 0:
                cleanup_script_logger.info("No empty content records found. Database is clean.")
                return
            
            # Clean up empty content
            cleanup_script_logger.info("Starting cleanup of empty content records")
            deleted_count = await cleanup_empty_tg_content(session)
            
            # Validate after cleanup
            cleanup_script_logger.info("Validating content integrity after cleanup")
            validation_after = await validate_tg_content_integrity(session)
            
            # Summary
            cleanup_script_logger.info(
                "Content cleanup completed successfully",
                deleted_records=deleted_count,
                integrity_before=f"{validation_before['integrity_score']:.2f}%",
                integrity_after=f"{validation_after['integrity_score']:.2f}%"
            )
            
        finally:
            await session.close()
            
    except Exception as e:
        cleanup_script_logger.error("Error during content cleanup", error=str(e))
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
